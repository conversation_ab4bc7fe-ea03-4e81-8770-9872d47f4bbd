using PEMTestSystem.Models.Devices;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// ITECH IT-M3900D 电源设备驱动
    /// 基于SCPI协议实现电源控制功能
    /// </summary>
    public class ItechPowerSupplyDriver : ScpiSerialBase, IPowerSupply
    {
        // SCPI命令定义
        private const string CMD_SET_VOLTAGE = "VOLT";
        private const string CMD_SET_CURRENT = "CURR";
        private const string CMD_SET_FUNCTION = "FUNC";
        private const string CMD_OUTPUT_STATE = "OUTP";
        private const string CMD_MEASURE_VOLTAGE = "MEAS:VOLT?";
        private const string CMD_MEASURE_CURRENT = "MEAS:CURR?";
        private const string CMD_MEASURE_POWER = "MEAS:POW?";
        private const string CMD_QUERY_VOLTAGE = "VOLT?";
        private const string CMD_QUERY_CURRENT = "CURR?";
        private const string CMD_QUERY_OUTPUT = "OUTP?";
        private const string CMD_CLEAR_PROTECTION = "OUTP:PROT:CLE";
        private const string CMD_SYSTEM_ERROR = "SYST:ERR?";
        private const string CMD_RESET = "*RST";

        // 设备规格参数
        private const double MIN_VOLTAGE = 0.0;
        private const double MAX_VOLTAGE = 150.0; // 根据具体型号调整
        private const double MIN_CURRENT = 0.0;
        private const double MAX_CURRENT = 30.0;  // 根据具体型号调整
        private const double VOLTAGE_ACCURACY = 0.01;
        private const double CURRENT_ACCURACY = 0.001;

        // 属性字段
        private double _currentVoltage;
        private double _setVoltage;
        private double _currentCurrent;
        private double _setCurrent;
        private double _currentPower;
        private bool _isOutputEnabled;
        private PowerSupplyMode _workingMode;

        // 事件
        public event EventHandler<PowerSupplyDataChangedEventArgs>? DataChanged;

        // 属性实现
        public double CurrentVoltage => _currentVoltage;
        public double SetVoltage => _setVoltage;
        public double CurrentCurrent => _currentCurrent;
        public double SetCurrent => _setCurrent;
        public double CurrentPower => _currentPower;
        public bool IsOutputEnabled => _isOutputEnabled;
        public PowerSupplyMode WorkingMode => _workingMode;

        // Type属性由基类提供，在构造函数中设置

        public ItechPowerSupplyDriver(
            string portName,
            int baudRate,
            string deviceId,
            string deviceName)
            : base(portName, baudRate, deviceId, Models.Devices.DeviceType.PowerSupply, deviceName, "ITECH IT-M3900D")
        {
            _workingMode = PowerSupplyMode.ConstantVoltage; // 默认恒压模式
            App.AlarmService.Info("设备初始化", $"ITECH电源 {deviceName} 驱动初始化完成");
        }

        #region 连接和初始化

        public override async Task<bool> ConnectAsync()
        {
            try
            {
                Status = DeviceStatus.Connecting;
                App.AlarmService.Info("设备连接", $"正在连接ITECH电源 {DeviceName}");

                var connected = await base.ConnectAsync();
                if (!connected)
                {
                    Status = DeviceStatus.Error;
                    return false;
                }

                // 初始化设备状态
                await InitializeDeviceStateAsync();

                Status = DeviceStatus.Connected;
                App.AlarmService.Info("设备连接", $"ITECH电源 {DeviceName} 连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"ITECH电源 {DeviceName} 连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化设备状态
        /// </summary>
        private async Task InitializeDeviceStateAsync()
        {
            try
            {
                // 读取当前设置值
                _setVoltage = await GetSetVoltageAsync();
                _setCurrent = await GetSetCurrentAsync();
                _isOutputEnabled = await GetOutputStatusAsync();

                // 读取实时测量值
                await UpdateMeasurementDataAsync();

                App.AlarmService.Info("设备初始化", 
                    $"ITECH电源 {DeviceName} 状态初始化完成 - 设定电压: {_setVoltage:F2}V, 设定电流: {_setCurrent:F3}A, 输出: {(_isOutputEnabled ? "开启" : "关闭")}");
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("设备初始化", $"ITECH电源 {DeviceName} 状态初始化部分失败: {ex.Message}");
            }
        }

        #endregion

        #region 电压控制

        public async Task<bool> SetVoltageAsync(double voltage)
        {
            try
            {
                if (!ValidateVoltage(voltage))
                {
                    return false;
                }

                var command = $"{CMD_SET_VOLTAGE} {voltage:F3}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _setVoltage = voltage;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 设置电压: {voltage:F2}V");
                    
                    // 触发数据变化事件
                    await UpdateMeasurementDataAsync();
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电压失败: {voltage:F2}V");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电压异常: {voltage:F2}V", ex);
                return false;
            }
        }

        public async Task<double> GetVoltageAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_MEASURE_VOLTAGE);
                var voltage = ParseNumericResponse(response);

                if (voltage.HasValue)
                {
                    _currentVoltage = voltage.Value;
                    return voltage.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 电压响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取电压失败", ex);
                return 0.0;
            }
        }

        public async Task<double> GetSetVoltageAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_QUERY_VOLTAGE);
                var voltage = ParseNumericResponse(response);

                if (voltage.HasValue)
                {
                    return voltage.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 设定电压响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取设定电压失败", ex);
                return 0.0;
            }
        }

        #endregion

        #region 电流控制

        public async Task<bool> SetCurrentAsync(double current)
        {
            try
            {
                if (!ValidateCurrent(current))
                {
                    return false;
                }

                var command = $"{CMD_SET_CURRENT} {current:F3}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _setCurrent = current;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 设置电流: {current:F3}A");
                    
                    // 触发数据变化事件
                    await UpdateMeasurementDataAsync();
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电流失败: {current:F3}A");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电流异常: {current:F3}A", ex);
                return false;
            }
        }

        public async Task<double> GetCurrentAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_MEASURE_CURRENT);
                var current = ParseNumericResponse(response);

                if (current.HasValue)
                {
                    _currentCurrent = current.Value;
                    return current.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 电流响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取电流失败", ex);
                return 0.0;
            }
        }

        public async Task<double> GetSetCurrentAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_QUERY_CURRENT);
                var current = ParseNumericResponse(response);

                if (current.HasValue)
                {
                    return current.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 设定电流响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取设定电流失败", ex);
                return 0.0;
            }
        }

        #endregion

        #region 功率监测

        public async Task<double> GetPowerAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_MEASURE_POWER);
                var power = ParseNumericResponse(response);

                if (power.HasValue)
                {
                    _currentPower = power.Value;
                    return power.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 功率响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取功率失败", ex);
                return 0.0;
            }
        }

        #endregion

        #region 输出控制

        public async Task<bool> EnableOutputAsync()
        {
            return await SetOutputAsync(true);
        }

        public async Task<bool> DisableOutputAsync()
        {
            return await SetOutputAsync(false);
        }

        public async Task<bool> SetOutputAsync(bool enabled)
        {
            try
            {
                var command = $"{CMD_OUTPUT_STATE} {(enabled ? "ON" : "OFF")}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _isOutputEnabled = enabled;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 输出{(enabled ? "开启" : "关闭")}");
                    
                    // 触发数据变化事件
                    await UpdateMeasurementDataAsync();
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 输出控制失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 输出控制异常", ex);
                return false;
            }
        }

        public async Task<bool> GetOutputStatusAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_QUERY_OUTPUT);
                var status = ParseBooleanResponse(response);

                if (status.HasValue)
                {
                    return status.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 输出状态响应解析失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取输出状态失败", ex);
                return false;
            }
        }

        #endregion

        #region 工作模式控制

        public async Task<bool> SetModeAsync(PowerSupplyMode mode)
        {
            try
            {
                var modeCommand = mode == PowerSupplyMode.ConstantVoltage ? "VOLT" : "CURR";
                var command = $"{CMD_SET_FUNCTION} {modeCommand}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _workingMode = mode;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 设置工作模式: {(mode == PowerSupplyMode.ConstantVoltage ? "恒压" : "恒流")}");
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置工作模式失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置工作模式异常", ex);
                return false;
            }
        }

        #endregion

        #region 保护和错误处理

        public async Task<bool> ClearProtectionAsync()
        {
            try
            {
                var success = await SendCommandAsync(CMD_CLEAR_PROTECTION);
                if (success)
                {
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 清除保护状态");
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 清除保护状态失败");
                }
                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 清除保护状态异常", ex);
                return false;
            }
        }

        public async Task<string?> GetErrorAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_SYSTEM_ERROR);
                if (!string.IsNullOrEmpty(response) && !response.Contains("No error"))
                {
                    App.AlarmService.Warning("电源状态", $"ITECH电源 {DeviceName} 检测到错误: {response}");
                    return response;
                }
                return null;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 查询错误信息失败", ex);
                return null;
            }
        }

        #endregion

        #region 设备信息和重置

        public override async Task<bool> ResetAsync()
        {
            try
            {
                App.AlarmService.Info("电源控制", $"正在重置ITECH电源 {DeviceName}");

                var success = await SendCommandAsync(CMD_RESET);
                if (success)
                {
                    // 等待设备重置完成
                    await Task.Delay(2000);

                    // 重新初始化设备状态
                    await InitializeDeviceStateAsync();

                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 重置完成");
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 重置失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 重置异常", ex);
                return false;
            }
        }

        public override async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            try
            {
                // 获取设备识别信息
                var idnResponse = await SendQueryAsync("*IDN?");

                // 更新测量数据
                await UpdateMeasurementDataAsync();

                LastCommunicationTime = DateTime.Now;

                return new DeviceInfo
                {
                    DeviceId = DeviceId,
                    DeviceName = DeviceName,
                    Model = Model,
                    Version = "1.0",
                    SerialNumber = ExtractSerialNumber(idnResponse),
                    Properties = new Dictionary<string, object>
                    {
                        ["Identification"] = idnResponse ?? "Unknown",
                        ["CurrentVoltage"] = _currentVoltage,
                        ["SetVoltage"] = _setVoltage,
                        ["CurrentCurrent"] = _currentCurrent,
                        ["SetCurrent"] = _setCurrent,
                        ["CurrentPower"] = _currentPower,
                        ["IsOutputEnabled"] = _isOutputEnabled,
                        ["WorkingMode"] = _workingMode.ToString(),
                        ["MinVoltage"] = MIN_VOLTAGE,
                        ["MaxVoltage"] = MAX_VOLTAGE,
                        ["MinCurrent"] = MIN_CURRENT,
                        ["MaxCurrent"] = MAX_CURRENT,
                        ["VoltageAccuracy"] = VOLTAGE_ACCURACY,
                        ["CurrentAccuracy"] = CURRENT_ACCURACY
                    }
                };
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备信息", $"ITECH电源 {DeviceName} 获取设备信息失败", ex);
                throw;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 更新测量数据
        /// </summary>
        private async Task UpdateMeasurementDataAsync()
        {
            try
            {
                _currentVoltage = await GetVoltageAsync();
                _currentCurrent = await GetCurrentAsync();
                _currentPower = await GetPowerAsync();
                _isOutputEnabled = await GetOutputStatusAsync();
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("电源监测", $"ITECH电源 {DeviceName} 更新测量数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证电压值
        /// </summary>
        private bool ValidateVoltage(double voltage)
        {
            if (voltage < MIN_VOLTAGE || voltage > MAX_VOLTAGE)
            {
                App.AlarmService.Warning("参数验证", $"ITECH电源 {DeviceName} 电压值超出范围: {voltage:F2}V (范围: {MIN_VOLTAGE}-{MAX_VOLTAGE}V)");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 验证电流值
        /// </summary>
        private bool ValidateCurrent(double current)
        {
            if (current < MIN_CURRENT || current > MAX_CURRENT)
            {
                App.AlarmService.Warning("参数验证", $"ITECH电源 {DeviceName} 电流值超出范围: {current:F3}A (范围: {MIN_CURRENT}-{MAX_CURRENT}A)");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 从IDN响应中提取序列号
        /// </summary>
        private string ExtractSerialNumber(string? idnResponse)
        {
            if (string.IsNullOrEmpty(idnResponse))
                return "Unknown";

            var parts = idnResponse.Split(',');
            return parts.Length >= 3 ? parts[2].Trim() : "Unknown";
        }

        /// <summary>
        /// 触发数据变化事件
        /// </summary>
        private void OnDataChanged()
        {
            try
            {
                DataChanged?.Invoke(this, new PowerSupplyDataChangedEventArgs(
                    DeviceId, _currentVoltage, _currentCurrent, _currentPower, _isOutputEnabled, _workingMode));
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("事件处理", $"ITECH电源 {DeviceName} 数据变化事件处理失败: {ex.Message}");
            }
        }

        #endregion
    }
}
